import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// 体重变化列表组件
class WeightListWidget extends StatelessWidget {
  final List<Map<String, dynamic>> data;
  final Color color;

  const WeightListWidget({super.key, required this.data, required this.color});

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Text('暂无体重记录', style: TextStyle(color: Colors.grey, fontSize: 16)),
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: data.length,
      separatorBuilder: (context, index) => Divider(height: 1, color: Colors.grey[200]),
      itemBuilder: (context, index) => _buildListItem(data[index], index),
    );
  }

  /// 构建列表项
  Widget _buildListItem(Map<String, dynamic> item, int index) {
    final recordDate = item['record_date'];
    final weight = item['weight'] as double? ?? 0.0;

    // 安全处理日期
    DateTime? date;
    String formattedDate = '未知时间';
    if (recordDate != null) {
      try {
        date = DateTime.parse(recordDate.toString());
        formattedDate = DateFormat('MM月dd日 HH:mm').format(date);
      } catch (e) {
        formattedDate = '日期格式错误';
      }
    }

    // 计算体重变化（与前一条记录比较）
    String? changeText;
    Color? changeColor;

    if (index < data.length - 1) {
      final previousWeight = data[index + 1]['weight'] as double? ?? 0.0;
      final change = weight - previousWeight;

      if (change != 0) {
        final sign = change > 0 ? '+' : '';
        changeText = '$sign${change.toStringAsFixed(1)}kg';
        changeColor = change > 0 ? Colors.red : Colors.green;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // 时间图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(20)),
            child: Icon(Icons.monitor_weight, color: color, size: 20),
          ),
          const SizedBox(width: 12),

          // 日期时间
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  formattedDate,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Colors.black87),
                ),
                if (changeText != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    '变化: $changeText',
                    style: TextStyle(fontSize: 12, color: changeColor, fontWeight: FontWeight.w500),
                  ),
                ],
              ],
            ),
          ),

          // 体重值
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${weight.toStringAsFixed(1)}kg',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
