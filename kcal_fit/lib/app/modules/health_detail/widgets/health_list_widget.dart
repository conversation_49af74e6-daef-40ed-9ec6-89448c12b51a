import 'package:flutter/material.dart';
import 'package:health/health.dart';
import '../../../shared/models/health_data_type.dart';

/// 通用健康数据列表组件
class HealthListWidget extends StatelessWidget {
  final List<HealthDataPoint> data;
  final HealthDataTypeEnum dataType;
  final Color color;

  const HealthListWidget({super.key, required this.data, required this.dataType, required this.color});

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    // 按时间排序
    final sortedData = List<HealthDataPoint>.from(data);
    sortedData.sort((a, b) => a.dateFrom.compareTo(b.dateFrom));

    return ListView.builder(
      shrinkWrap: true,
      itemCount: sortedData.length,
      itemBuilder: (context, index) {
        final point = sortedData[index];
        return _buildListItem(point);
      },
    );
  }

  /// 构建列表项
  Widget _buildListItem(HealthDataPoint point) {
    final config = HealthDataTypeConfigs.getConfig(dataType);
    final value = (point.value as NumericHealthValue).numericValue.toDouble();
    final timeRange = _formatTimeRange(point.dateFrom, point.dateTo);
    final formattedValue = _formatValue(value);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(8)),
            child: Icon(config.icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(timeRange, style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
                const SizedBox(height: 2),
                Text('来源: ${point.sourceName}', style: TextStyle(fontSize: 11, color: Colors.grey[600])),
              ],
            ),
          ),
          Text(formattedValue, style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: color)),
        ],
      ),
    );
  }

  /// 格式化时间范围
  String _formatTimeRange(DateTime from, DateTime to) {
    final timeFrom = from;
    final timeTo = to;

    return '${timeFrom.hour.toString().padLeft(2, '0')}:${timeFrom.minute.toString().padLeft(2, '0')} - ${timeTo.hour.toString().padLeft(2, '0')}:${timeTo.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化数值
  String _formatValue(double value) {
    final config = HealthDataTypeConfigs.getConfig(dataType);

    switch (dataType) {
      case HealthDataTypeEnum.distance:
        final distanceInKm = value / 1000;
        return '${distanceInKm.toStringAsFixed(2)} ${config.unit}';
      case HealthDataTypeEnum.steps:
      case HealthDataTypeEnum.flights:
        return '${value.toInt()} ${config.unit}';
      case HealthDataTypeEnum.activeCalories:
      case HealthDataTypeEnum.basalCalories:
      case HealthDataTypeEnum.totalCalories:
        return '${value.toInt()} ${config.unit}';
      case HealthDataTypeEnum.weightChange:
        return '${value.toStringAsFixed(1)} ${config.unit}';
    }
  }
}
