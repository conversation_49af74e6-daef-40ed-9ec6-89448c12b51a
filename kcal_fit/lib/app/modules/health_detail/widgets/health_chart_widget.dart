import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:health/health.dart';
import '../../../shared/models/health_data_type.dart';

/// 通用健康数据图表组件
class HealthChartWidget extends StatelessWidget {
  final List<HealthDataPoint> data;
  final HealthDataTypeEnum dataType;
  final Color color;

  const HealthChartWidget({super.key, required this.data, required this.dataType, required this.color});

  @override
  Widget build(BuildContext context) {
    if (data.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    return _buildChart();
  }

  /// 构建图表
  Widget _buildChart() {
    // 按小时分组数据
    final Map<int, double> hourlyData = {};

    for (var point in data) {
      final hour = point.dateFrom.hour;
      final value = (point.value as NumericHealthValue).numericValue.toDouble();

      // 根据数据类型处理数值
      double processedValue = value;
      if (dataType == HealthDataTypeEnum.distance) {
        processedValue = value / 1000; // 转换为公里
      }

      hourlyData[hour] = (hourlyData[hour] ?? 0) + processedValue;
    }

    // 创建图表数据点
    final List<FlSpot> spots = [];
    double maxValue = 0;

    for (int hour = 0; hour < 24; hour++) {
      final value = hourlyData[hour] ?? 0;
      if (value > 0) {
        spots.add(FlSpot(hour.toDouble(), value));
        if (value > maxValue) maxValue = value;
      }
    }

    if (spots.isEmpty) {
      return const Center(child: Text('暂无数据', style: TextStyle(color: Colors.grey)));
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: _getHorizontalInterval(maxValue),
          verticalInterval: 4,
          getDrawingHorizontalLine: (value) => FlLine(color: Colors.grey.withValues(alpha: 0.3), strokeWidth: 1),
          getDrawingVerticalLine: (value) => FlLine(color: Colors.grey.withValues(alpha: 0.3), strokeWidth: 1),
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 4,
              getTitlesWidget: (double value, TitleMeta meta) {
                return SideTitleWidget(
                  axisSide: meta.axisSide,
                  child: Text('${value.toInt()}:00', style: const TextStyle(color: Colors.grey, fontSize: 10)),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: _getHorizontalInterval(maxValue),
              reservedSize: 50,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(_formatYAxisLabel(value), style: const TextStyle(color: Colors.grey, fontSize: 10));
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: true, border: Border.all(color: Colors.grey.withValues(alpha: 0.3))),
        minX: 0,
        maxX: 23,
        minY: 0,
        maxY: maxValue * 1.1,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: color,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(show: true, color: color.withValues(alpha: 0.1)),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final flSpot = barSpot;
                return LineTooltipItem(
                  '${flSpot.x.toInt()}:00\n${_formatTooltipValue(flSpot.y)}',
                  const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 12),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  /// 获取水平间隔
  double _getHorizontalInterval(double maxValue) {
    if (maxValue <= 0) return 1;
    return maxValue / 5;
  }

  /// 格式化Y轴标签
  String _formatYAxisLabel(double value) {
    final config = HealthDataTypeConfigs.getConfig(dataType);
    switch (dataType) {
      case HealthDataTypeEnum.distance:
        return '${value.toStringAsFixed(1)}km';
      case HealthDataTypeEnum.steps:
      case HealthDataTypeEnum.flights:
        return '${value.toInt()}${config.unit}';
      case HealthDataTypeEnum.activeCalories:
      case HealthDataTypeEnum.basalCalories:
      case HealthDataTypeEnum.totalCalories:
        return '${value.toInt()}${config.unit}';
      case HealthDataTypeEnum.weightChange:
        return '${value.toStringAsFixed(1)}${config.unit}';
    }
  }

  /// 格式化提示值
  String _formatTooltipValue(double value) {
    final config = HealthDataTypeConfigs.getConfig(dataType);
    switch (dataType) {
      case HealthDataTypeEnum.distance:
        return '${value.toStringAsFixed(2)} ${config.unit}';
      case HealthDataTypeEnum.steps:
      case HealthDataTypeEnum.flights:
        return '${value.toInt()} ${config.unit}';
      case HealthDataTypeEnum.activeCalories:
      case HealthDataTypeEnum.basalCalories:
      case HealthDataTypeEnum.totalCalories:
        return '${value.toInt()} ${config.unit}';
      case HealthDataTypeEnum.weightChange:
        return '${value.toStringAsFixed(1)} ${config.unit}';
    }
  }
}
