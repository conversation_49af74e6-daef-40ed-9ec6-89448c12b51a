import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/controllers/base_controller.dart';
import '../../../data/services/hive/hive_service.dart';
import '../../../data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../../../core/constants/health_goals.dart';

/// 目标设定控制器
/// 负责管理用户的健康目标设定，包括目标体重、卡路里摄入、活动水平等
class GoalSettingController extends BaseController {
  /// 数据访问对象
  final BodyDataDao _bodyDataDao = BodyDataDao();

  /// 表单相关
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController targetWeightController = TextEditingController();

  /// 目标类型（多选，存储逗号分隔的目标类型）
  final RxString _selectedGoalTypes = ''.obs;
  final List<String> goalTypes = HealthGoals.options;

  /// 目标数据
  final RxDouble _targetWeight = 0.0.obs;
  final RxInt _targetDays = 90.obs;
  final RxString _activityLevel = '轻度活动'.obs;

  /// 保存状态
  final RxBool _isSaving = false.obs;

  /// 当前体重（从用户数据获取）
  final RxDouble currentWeight = 0.0.obs;
  final RxDouble currentHeight = 0.0.obs;
  final RxDouble currentBMI = 0.0.obs;

  /// 活动水平选项
  final List<Map<String, String>> activityLevels = [
    {'level': '久坐不动', 'description': '办公室工作，很少运动'},
    {'level': '轻度活动', 'description': '轻松运动1-3天/周'},
    {'level': '中度活动', 'description': '中等运动3-5天/周'},
    {'level': '高度活动', 'description': '剧烈运动6-7天/周'},
    {'level': '极高活动', 'description': '体力工作或每天2次训练'},
  ];

  /// 目标期限选项
  final List<int> targetDaysOptions = [30, 60, 90, 120, 180, 365];

  // Getter方法，提供对私有变量的只读访问
  String get selectedGoalTypes => _selectedGoalTypes.value;
  double get targetWeight => _targetWeight.value;
  int get targetDays => _targetDays.value;
  String get activityLevel => _activityLevel.value;
  bool get isSaving => _isSaving.value;

  @override
  void onInit() {
    super.onInit();
    _cleanupOldGoalTypeData(); // 清理旧数据
    _loadCurrentData();
    _loadGoalData();
  }

  /// 刷新目标类型
  Future<void> refreshGoalType() async {
    await _loadGoalTypes();
  }

  /// 清理旧的goalType数据（迁移用）
  Future<void> _cleanupOldGoalTypeData() async {
    try {
      // 检查是否存在旧的goalType数据
      final oldGoalType = await HiveService.getData("goals", "goalType");
      if (oldGoalType != null) {
        // 如果用户的健康目标为空，将旧的goalType迁移过去
        final currentTargetDescription = await HiveService.getData("user", "targetDescription");
        if (currentTargetDescription == null || currentTargetDescription.isEmpty) {
          await HiveService.saveData("user", "targetDescription", oldGoalType);
        }
        // 删除旧的goalType数据
        await HiveService.deleteData("goals", "goalType");
      }
    } catch (e) {
      // 忽略清理错误，不影响主要功能
    }
  }

  @override
  void onClose() {
    targetWeightController.dispose();
    super.onClose();
  }

  /// 加载当前身体数据
  Future<void> _loadCurrentData() async {
    try {
      // 从SQLite数据库加载最新的身体数据
      final latestBodyData = await _bodyDataDao.getLatestBodyData();

      if (latestBodyData != null) {
        // 设置当前体重和身高
        if (latestBodyData.weight != null) {
          currentWeight.value = latestBodyData.weight!; // 直接使用double值
        }
        if (latestBodyData.height != null) {
          currentHeight.value = latestBodyData.height!.toDouble();
        }

        // 使用数据库中的BMI，如果没有则计算
        if (latestBodyData.bmi != null) {
          currentBMI.value = latestBodyData.bmi!;
        } else if (currentWeight.value > 0 && currentHeight.value > 0) {
          final heightInMeters = currentHeight.value / 100;
          currentBMI.value = currentWeight.value / (heightInMeters * heightInMeters);
        }
      } else {
        // 如果数据库中没有数据，尝试从Hive读取（向后兼容）
        final weightData = await HiveService.getData("bodyData", "weight");
        final heightData = await HiveService.getData("bodyData", "height");

        if (weightData != null) {
          currentWeight.value = double.tryParse(weightData.toString()) ?? 0.0;
        }
        if (heightData != null) {
          currentHeight.value = double.tryParse(heightData.toString()) ?? 0.0;
        }

        // 计算BMI
        if (currentWeight.value > 0 && currentHeight.value > 0) {
          final heightInMeters = currentHeight.value / 100;
          currentBMI.value = currentWeight.value / (heightInMeters * heightInMeters);
        }
      }
    } catch (e) {
      // 静默处理加载错误
    }
  }

  /// 加载目标数据
  Future<void> _loadGoalData() async {
    try {
      setLoading(true);

      // 加载目标类型
      await _loadGoalTypes();

      // 从Hive加载其他目标数据（不包括goalType）
      final savedTargetWeight = await HiveService.getData("goals", "targetWeight");
      final savedTargetDays = await HiveService.getData("goals", "targetDays");
      final savedActivityLevel = await HiveService.getData("goals", "activityLevel");

      if (savedTargetWeight != null) {
        _targetWeight.value = double.tryParse(savedTargetWeight.toString()) ?? 0.0;
        targetWeightController.text = targetWeight.toString();
      }
      if (savedTargetDays != null) {
        _targetDays.value = int.tryParse(savedTargetDays.toString()) ?? 90;
      }
      if (savedActivityLevel != null) {
        _activityLevel.value = savedActivityLevel;
      }
    } catch (e) {
      // ignore: avoid_print
      print('加载目标数据失败: $e');
    } finally {
      setLoading(false);
    }
  }

  /// 加载目标类型（从goals存储中读取）
  Future<void> _loadGoalTypes() async {
    try {
      final savedGoalTypes = await HiveService.getData("goals", "goalTypes");
      if (savedGoalTypes != null && savedGoalTypes.isNotEmpty) {
        _selectedGoalTypes.value = savedGoalTypes;
      } else {
        // 如果没有保存的目标类型，不设置默认值，保持空
        _selectedGoalTypes.value = '';
      }
    } catch (e) {
      // ignore: avoid_print
      print('加载目标类型失败: $e');
    }
  }

  /// 切换目标类型选择状态（多选）
  void toggleGoalType(String type) {
    List<String> currentGoals = selectedGoalTypes.isNotEmpty ? selectedGoalTypes.split(',') : [];

    if (currentGoals.contains(type)) {
      currentGoals.remove(type);
    } else {
      currentGoals.add(type);
    }

    _selectedGoalTypes.value = currentGoals.join(',');
  }

  /// 获取当前选中的目标类型列表
  List<String> getSelectedGoalTypes() {
    return selectedGoalTypes.isNotEmpty ? selectedGoalTypes.split(',') : [];
  }

  /// 检查目标类型是否被选中
  bool isGoalTypeSelected(String type) {
    return getSelectedGoalTypes().contains(type);
  }

  /// 更新目标体重
  void updateTargetWeight(String value) {
    final weight = double.tryParse(value);
    if (weight != null && weight > 0) {
      _targetWeight.value = weight;
    }
  }

  /// 更新目标期限
  void updateTargetDays(int days) {
    _targetDays.value = days;
  }

  /// 更新活动水平
  void updateActivityLevel(String level) {
    _activityLevel.value = level;
  }

  /// 计算推荐卡路里（保留方法以避免其他地方调用出错，但不再执行实际逻辑）
  void calculateRecommendedCalories() {
    // 由于已移除每日卡路里目标功能，此方法保留为空实现
    // 避免其他地方调用此方法时出错
  }

  /// 保存目标设定
  Future<void> saveGoalSettings() async {
    // 验证表单
    if (!formKey.currentState!.validate()) {
      Get.snackbar(
        '验证失败',
        '请检查输入的信息是否完整和正确',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.orange.shade100,
        colorText: Colors.orange.shade800,
        duration: const Duration(seconds: 2),
      );
      return;
    }

    try {
      _isSaving.value = true;

      // 保存到Hive
      await HiveService.saveData("goals", "goalTypes", selectedGoalTypes);
      await HiveService.saveData("goals", "targetWeight", targetWeight);
      await HiveService.saveData("goals", "targetDays", targetDays);
      await HiveService.saveData("goals", "activityLevel", activityLevel);
      await HiveService.saveData("goals", "createdTime", DateTime.now().toIso8601String());

      // 先返回上一页
      Get.back();

      // 然后显示成功提示
      Get.snackbar(
        '保存成功',
        '目标设定已保存',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        '保存失败',
        '保存目标设定失败: $e',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 3),
      );
    } finally {
      _isSaving.value = false;
    }
  }

  /// 重置目标设定
  void resetGoalSettings() {
    _selectedGoalTypes.value = '';
    _targetWeight.value = 0.0;
    _targetDays.value = 90;
    _activityLevel.value = '轻度活动';
    targetWeightController.clear();
  }

  /// 获取目标描述
  String getGoalDescription() {
    if (currentWeight.value <= 0 || targetWeight <= 0) {
      return '请设置目标体重';
    }

    final weightDiff = targetWeight - currentWeight.value;
    final weightDiffAbs = weightDiff.abs();
    final direction = weightDiff > 0 ? '增加' : '减少';

    // ignore: unnecessary_brace_in_string_interps
    return '目标${direction} ${weightDiffAbs.toStringAsFixed(1)}kg，预计${targetDays}天完成';
  }

  /// 获取每周建议减重/增重
  String getWeeklyWeightChange() {
    if (currentWeight.value <= 0 || targetWeight <= 0 || targetDays <= 0) {
      return '0.0kg/周';
    }

    final weightDiff = (targetWeight - currentWeight.value).abs();
    final weeks = targetDays / 7;
    final weeklyChange = weightDiff / weeks;

    return '${weeklyChange.toStringAsFixed(1)}kg/周';
  }
}
