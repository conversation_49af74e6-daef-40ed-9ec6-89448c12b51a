import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../core/utils/logger_util.dart';
import '../../../../core/services/user_guide_service.dart';
import '../../../shared/controllers/base_controller.dart';
import '../../../data/services/hive/hive_service.dart';
import '../../../data/services/sqflite/dao/impl/body_data_dao.dart';
import '../../../data/services/sqflite/dao/impl/diet_analysis_dao.dart';

/// 我的页面控制器
/// 负责管理用户个人信息和身体数据的展示
class MyController extends BaseController {
  /// 用户基本信息数据
  final RxMap<String, String> _userData =
      <String, String>{"nickname": "", "gender": "", "birthDate": "", "targetDescription": ""}.obs;

  /// 身体数据信息
  final RxMap<String, String> _bodyData =
      <String, String>{"width": "-", "height": "-", "bmi": "-", "bodyFatPercentage": "-"}.obs;

  /// 身体数据访问对象
  final BodyDataDao _bodyDataDao = BodyDataDao();

  /// 饮食分析数据访问对象
  final DietAnalysisDao _dietAnalysisDao = DietAnalysisDao();

  /// 统计数据
  final RxInt _recordDays = 0.obs;
  final RxInt _continuousCheckIn = 0.obs;
  final RxDouble _weightChange = 0.0.obs;

  /// 应用版本信息
  final RxString _appVersion = '1.0.0'.obs;
  final RxString _buildNumber = '1'.obs;

  /// 是否已经检查过身体数据引导（避免重复检查）
  bool _hasCheckedBodyDataGuide = false;

  // Getter方法，提供对私有变量的只读访问
  Map<String, String> get userData => _userData;
  Map<String, String> get bodyData => _bodyData;

  // 统计数据的getter方法
  int get recordDays => _recordDays.value;
  int get continuousCheckIn => _continuousCheckIn.value;
  double get weightChange => _weightChange.value;

  // 应用版本的getter方法
  String get appVersion => _appVersion.value;
  String get buildNumber => _buildNumber.value;
  String get fullVersion => 'v${_appVersion.value} (${_buildNumber.value})';

  /// 数据加载状态标记
  bool _isDataLoaded = false;

  @override
  void onInit() {
    super.onInit();
    LoggerUtil.d('MyController - 初始化');

    // 优化：延迟加载非关键数据，优先显示UI
    Future.microtask(() => loadAppVersion());

    // 延迟加载用户数据，避免阻塞初始化
    Future.delayed(const Duration(milliseconds: 200), () {
      if (!_isDataLoaded) {
        _loadInitialData();
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    LoggerUtil.d('MyController - 就绪');
    // 如果数据还未加载，立即加载
    if (!_isDataLoaded) {
      _loadInitialData();
    }
  }

  /// 初始化数据加载
  Future<void> _loadInitialData() async {
    if (_isDataLoaded) return;

    _isDataLoaded = true;
    await Future.wait([loadBodyData(), loadStatisticsData()]);
  }

  @override
  Future<void> onRefresh() async {
    // 重置检查状态，允许重新检查引导
    _hasCheckedBodyDataGuide = false;
    await loadBodyData();
    await loadStatisticsData();
  }

  /// 刷新所有数据
  /// 包括身体数据和统计数据
  Future<void> refreshData() async {
    // 重置数据加载状态，允许重新加载
    _isDataLoaded = false;
    await Future.wait([loadBodyData(), loadStatisticsData()]);
    _isDataLoaded = true;
    LoggerUtil.i('我的页面所有数据刷新完成');
  }

  /// 加载用户数据和身体数据
  /// 从Hive和数据库中获取用户的个人信息和最新的身体数据
  Future<void> loadBodyData() async {
    await executeAsync(() async {
      // 加载用户基本信息（昵称等）
      final allUserData = await HiveService.getAllData("user");
      final userDataMap = Map<String, String>.from(allUserData);

      // 更新用户数据
      _userData.assignAll(userDataMap);

      // 加载最新的身体数据
      final latestData = await _bodyDataDao.getLatestBodyData();
      if (latestData != null) {
        _bodyData.assignAll({
          "width": latestData.weight.toString(),
          "height": latestData.height.toString(),
          "bmi": latestData.bmi.toString(),
          "bodyFatPercentage": latestData.bodyFatPercentage.toString(),
        });

        // 从body_data表中更新性别和出生日期到userData
        if (latestData.gender != null) {
          _userData['gender'] = latestData.gender!;
        }
        if (latestData.birthDate != null) {
          _userData['birthDate'] = latestData.birthDate!.toIso8601String();
        }

        setEmpty(false);
        LoggerUtil.i('身体数据加载成功: ${latestData.toString()}');
      } else {
        // 如果没有身体数据，设置为空状态
        setEmpty(true);
        LoggerUtil.w('未找到身体数据');

        // 智能检查是否需要显示身体数据引导
        _checkBodyDataGuideIfNeeded();
      }

      LoggerUtil.i('用户数据加载成功: $userDataMap');
    });
  }

  /// 获取用户昵称
  String get nickname => _userData['nickname'] ?? '';

  /// 获取用户性别
  String get gender => _userData['gender'] ?? '';

  /// 获取用户生日
  String get birthDate => _userData['birthDate'] ?? '';

  /// 获取目标描述
  String get targetDescription => _userData['targetDescription'] ?? '';

  /// 获取体重
  String get weight => _bodyData['width'] ?? '-';

  /// 获取身高
  String get height => _bodyData['height'] ?? '-';

  /// 获取BMI
  String get bmi => _bodyData['bmi'] ?? '-';

  /// 获取体脂率
  String get bodyFatPercentage => _bodyData['bodyFatPercentage'] ?? '-';

  /// 重置身体数据引导检查状态
  /// 允许重新检查是否需要显示引导
  void resetBodyDataGuideCheck() {
    _hasCheckedBodyDataGuide = false;
    LoggerUtil.i('身体数据引导检查状态已重置');
  }

  /// 检查是否需要显示身体数据引导
  /// 无身体数据时就显示引导界面
  void _checkBodyDataGuideIfNeeded() {
    try {
      // 如果已经检查过，不再重复检查
      if (_hasCheckedBodyDataGuide) {
        return;
      }

      // 标记已检查，避免重复
      _hasCheckedBodyDataGuide = true;

      // 检查用户引导服务是否可用
      if (!Get.isRegistered<UserGuideService>()) {
        LoggerUtil.w('UserGuideService未注册，无法显示身体数据引导');
        return;
      }

      LoggerUtil.i('检测到无身体数据，准备显示引导');
      // 延迟一段时间，确保页面完全加载
      Future.delayed(const Duration(milliseconds: 1500), () {
        // 再次检查是否还需要显示（可能在延迟期间用户已经录入了数据）
        _showBodyDataGuideIfStillNeeded();
      });
    } catch (e) {
      LoggerUtil.e('检查身体数据引导失败: $e');
    }
  }

  /// 如果仍然需要，显示身体数据引导
  Future<void> _showBodyDataGuideIfStillNeeded() async {
    try {
      // 再次检查是否有身体数据（可能在延迟期间用户已经录入）
      final latestData = await _bodyDataDao.getLatestBodyData();
      if (latestData != null) {
        LoggerUtil.i('用户已录入身体数据，取消显示引导');
        return;
      }

      // 检查用户引导服务状态
      if (!Get.isRegistered<UserGuideService>()) {
        return;
      }

      final userGuideService = Get.find<UserGuideService>();

      // 显示身体数据引导
      LoggerUtil.i('从我的页面触发身体数据引导');
      await userGuideService.checkAndShowBodyDataGuideFromPage('我的页面');
    } catch (e) {
      LoggerUtil.e('显示身体数据引导失败: $e');
    }
  }

  /// 加载统计数据
  /// 包括记录天数、连续打卡天数和已减重量
  Future<void> loadStatisticsData() async {
    await executeAsync(() async {
      try {
        // 并行加载所有统计数据
        final results = await Future.wait([
          _dietAnalysisDao.getRecordDaysCount(),
          _dietAnalysisDao.getContinuousCheckInDays(),
          _bodyDataDao.getWeightChangeAmount(),
        ]);

        // 更新响应式变量
        _recordDays.value = results[0] as int;
        _continuousCheckIn.value = results[1] as int;
        _weightChange.value = results[2] as double;

        LoggerUtil.i(
          '统计数据加载成功: 记录天数=${_recordDays.value}, 连续打卡=${_continuousCheckIn.value}, 体重变化=${_weightChange.value}kg',
        );
      } catch (e) {
        LoggerUtil.e('加载统计数据失败: $e');
        // 设置默认值
        _recordDays.value = 0;
        _continuousCheckIn.value = 0;
        _weightChange.value = 0.0;
      }
    });
  }

  /// 显示体重变化详情页面
  Future<void> showWeightChangeDetail() async {
    try {
      // 获取最近两次体重记录
      final recentRecords = await _bodyDataDao.getRecentTwoBodyData();

      if (recentRecords.length < 2) {
        Get.snackbar('提示', '需要至少两次体重记录才能查看变化详情');
        return;
      }

      // 跳转到体重变化详情页面
      Get.toNamed('/health-detail', parameters: {'type': 'weightChange'});
    } catch (e) {
      LoggerUtil.e('显示体重变化详情失败: $e');
      Get.snackbar('错误', '获取体重数据失败');
    }
  }

  /// 加载应用版本信息
  /// 从设备获取应用的版本号和构建号
  Future<void> loadAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _appVersion.value = packageInfo.version;
      _buildNumber.value = packageInfo.buildNumber;
      LoggerUtil.i('应用版本信息加载成功: ${packageInfo.appName} v${packageInfo.version} (${packageInfo.buildNumber})');
    } catch (e) {
      LoggerUtil.e('加载应用版本信息失败: $e');
      // 保持默认值
    }
  }
}
