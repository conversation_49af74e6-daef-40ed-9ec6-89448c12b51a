import 'dart:convert';
import 'package:logger/logger.dart';

import '../../../../models/body_data.dart';
import '../../config/database_config.dart';
import '../../core/database_exception.dart';
import '../../core/database_service.dart';
import '../base/base_dao.dart';

final Logger logger = Logger();

/// 身体数据DAO
/// 负责身体数据的数据库操作
class BodyDataDao extends BaseDaoImpl<BodyData> {
  final DatabaseService _dbService = DatabaseService();

  @override
  String get tableName => DatabaseConfig.tableBodyData;

  @override
  Map<String, dynamic> toMap(BodyData entity) => entity.toMap();

  @override
  BodyData fromMap(Map<String, dynamic> map) => BodyData.fromMap(map);

  @override
  int? getId(BodyData entity) => entity.id;

  @override
  BodyData setId(BodyData entity, int id) {
    // 这里需要根据BodyData模型的实际结构来实现
    // 假设BodyData有copyWith方法
    return entity; // 临时返回，需要根据实际模型调整
  }

  @override
  Future<int> insert(BodyData entity) async {
    try {
      return await _dbService.insert(tableName, toMap(entity));
    } catch (e) {
      logger.e('添加身体数据记录失败: $e');
      rethrow;
    }
  }

  /// 添加身体数据记录（保持向后兼容）
  Future<int> addBodyData(BodyData bodyData) => insert(bodyData);

  @override
  Future<BodyData?> findById(int id) async {
    try {
      final result = await _dbService.findById(tableName, id);
      return result != null ? fromMap(result) : null;
    } catch (e) {
      logger.e('根据ID查询身体数据失败: $e');
      rethrow;
    }
  }

  /// 获取最新的身体数据记录
  Future<BodyData?> getLatestBodyData() async {
    try {
      final results = await _dbService.query(tableName, orderBy: 'record_date DESC', limit: 1);
      return results.isNotEmpty ? fromMap(results.first) : null;
    } catch (e) {
      logger.e('获取最新身体数据记录失败: $e');
      rethrow;
    }
  }

  /// 获取指定日期的身体数据记录
  Future<BodyData?> getBodyDataByDate(String date) async {
    try {
      final dateOnly = date.substring(0, 10); // 只取日期部分，不含时间
      final results = await _dbService.query(tableName, where: 'date(record_date) = ?', whereArgs: [dateOnly]);
      return results.isNotEmpty ? fromMap(results.first) : null;
    } catch (e) {
      logger.e('根据日期查询身体数据失败: $e');
      rethrow;
    }
  }

  @override
  Future<List<BodyData>> findAll() async {
    try {
      final results = await _dbService.query(tableName, orderBy: 'record_date DESC');
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('获取所有身体数据记录失败: $e');
      rethrow;
    }
  }

  /// 获取所有身体数据记录（保持向后兼容）
  Future<List<BodyData>> getAllBodyData() => findAll();

  @override
  Future<int> update(BodyData entity) async {
    try {
      final id = getId(entity);
      if (id == null) {
        throw DatabaseUpdateException('更新身体数据记录失败: 缺少ID');
      }
      logger.i('更新身体数据记录: ${jsonEncode(toMap(entity))}');
      return await _dbService.updateById(tableName, toMap(entity), id);
    } catch (e) {
      logger.e('更新身体数据记录失败: $e');
      rethrow;
    }
  }

  /// 更新身体数据记录（保持向后兼容）
  Future<int> updateBodyData(BodyData bodyData) => update(bodyData);

  @override
  Future<int> deleteById(int id) async {
    try {
      final result = await _dbService.deleteById(tableName, id);
      if (result == 0) {
        logger.w('删除失败: 未找到ID为$id的身体数据记录');
      }
      return result;
    } catch (e) {
      logger.e('删除身体数据记录失败: $e');
      rethrow;
    }
  }

  /// 删除身体数据记录（保持向后兼容）
  Future<int> deleteBodyData(int id) => deleteById(id);

  /// 获取特定时间范围内的身体数据记录
  Future<List<BodyData>> getBodyDataInRange(String startDate, String endDate) async {
    try {
      final results = await _dbService.query(
        tableName,
        where: 'date(record_date) BETWEEN ? AND ?',
        whereArgs: [startDate, endDate],
        orderBy: 'record_date ASC',
      );
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('获取时间范围内身体数据失败: $e');
      rethrow;
    }
  }

  /// 获取所有记录的日期列表（用于日历视图）
  Future<List<String>> getRecordDates() async {
    try {
      final results = await _dbService.query(
        tableName,
        columns: ['date(record_date) as date'],
        groupBy: 'date(record_date)',
        orderBy: 'date(record_date) DESC',
      );
      return results.map((map) => map['date'] as String).toList();
    } catch (e) {
      logger.e('获取记录日期列表失败: $e');
      rethrow;
    }
  }

  /// 获取身体数据统计信息（如平均体重、BMI等）
  Future<Map<String, dynamic>> getBodyDataStats() async {
    try {
      final results = await _dbService.query(
        tableName,
        columns: [
          'AVG(weight) as avg_weight',
          'AVG(bmi) as avg_bmi',
          'AVG(body_fat_percentage) as avg_body_fat',
          'MIN(weight) as min_weight',
          'MAX(weight) as max_weight',
          'COUNT(*) as record_count',
        ],
      );
      return results.isNotEmpty ? results.first : {};
    } catch (e) {
      logger.e('获取身体数据统计信息失败: $e');
      rethrow;
    }
  }

  /// 获取最近一段时间的体重变化趋势
  Future<List<Map<String, dynamic>>> getWeightTrend(int days) async {
    try {
      final date = DateTime.now().subtract(Duration(days: days));
      final dateStr = date.toIso8601String().substring(0, 10);

      return await _dbService.query(
        tableName,
        columns: ['record_date', 'weight'],
        where: 'date(record_date) >= ? AND weight IS NOT NULL',
        whereArgs: [dateStr],
        orderBy: 'record_date ASC',
      );
    } catch (e) {
      logger.e('获取体重变化趋势失败: $e');
      rethrow;
    }
  }

  /// 获取体重变化（从第一条记录到最后一条记录的差值）
  /// 返回体重变化值，正数表示增重，负数表示减重
  Future<double> getWeightChange() async {
    try {
      // 获取第一条记录（最早的记录）
      final firstResults = await _dbService.query(
        tableName,
        columns: ['weight'],
        where: 'weight IS NOT NULL',
        orderBy: 'record_date ASC',
        limit: 1,
      );

      // 获取最后一条记录（最新的记录）
      final lastResults = await _dbService.query(
        tableName,
        columns: ['weight'],
        where: 'weight IS NOT NULL',
        orderBy: 'record_date DESC',
        limit: 1,
      );

      if (firstResults.isEmpty || lastResults.isEmpty) {
        return 0.0;
      }

      final firstWeight = firstResults.first['weight'] as double? ?? 0.0;
      final lastWeight = lastResults.first['weight'] as double? ?? 0.0;

      // 返回体重变化（最新体重 - 最早体重）
      return lastWeight - firstWeight;
    } catch (e) {
      logger.e('获取体重变化失败: $e');
      return 0.0;
    }
  }

  /// 获取最近两次体重变化差值
  /// 返回最近两次体重记录的差值，正数表示增重，负数表示减重
  Future<double> getRecentWeightChange() async {
    try {
      // 获取最近两条记录
      final recentResults = await _dbService.query(
        tableName,
        columns: ['weight', 'record_date'],
        where: 'weight IS NOT NULL',
        orderBy: 'record_date DESC',
        limit: 2,
      );

      if (recentResults.length < 2) {
        return 0.0;
      }

      final latestWeight = recentResults[0]['weight'] as double? ?? 0.0;
      final previousWeight = recentResults[1]['weight'] as double? ?? 0.0;

      // 返回体重变化（最新体重 - 上一次体重）
      return latestWeight - previousWeight;
    } catch (e) {
      logger.e('获取最近两次体重变化失败: $e');
      return 0.0;
    }
  }

  /// 获取体重变化量（使用最近两次记录的差值）
  /// 正数表示增重，负数表示减重
  Future<double> getWeightChangeAmount() async {
    try {
      return await getRecentWeightChange();
    } catch (e) {
      logger.e('获取体重变化量失败: $e');
      return 0.0;
    }
  }

  /// 获取第一条身体数据记录（最早的记录）
  Future<BodyData?> getFirstBodyData() async {
    try {
      final results = await _dbService.query(tableName, orderBy: 'record_date ASC', limit: 1);
      return results.isNotEmpty ? fromMap(results.first) : null;
    } catch (e) {
      logger.e('获取第一条身体数据失败: $e');
      return null;
    }
  }

  /// 获取最近两次体重记录
  /// 返回按时间倒序排列的最近两条记录
  Future<List<BodyData>> getRecentTwoBodyData() async {
    try {
      final results = await _dbService.query(
        tableName,
        where: 'weight IS NOT NULL',
        orderBy: 'record_date DESC',
        limit: 2,
      );
      return results.map((map) => fromMap(map)).toList();
    } catch (e) {
      logger.e('获取最近两次体重记录失败: $e');
      return [];
    }
  }
}
